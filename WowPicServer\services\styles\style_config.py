"""
风格与API厂商映射配置
"""

# 风格与厂商映射配置
STYLE_PROVIDER_MAPPING = {
    # AIHubMix 厂商的风格（使用gpt-4o-image-vip + chat.completions）
    'cat_travel_v1': 'aihubmix',
    'pet_id_photo_v1': 'aihubmix',
    'japanese_cartoon_v1': 'aihubmix',

    # 兔子API厂商的风格（使用gpt-4o-image-vip + chat.completions）
    # 注意：这些风格实际上也支持n参数
    'cat_travel_v2': 'tuzi_chat',
    'pet_id_photo_v2': 'tuzi_chat',

    # 兔子API厂商的风格（使用gpt-image-1 + images.edit）
    # 注意：这些风格不支持n参数
    'ghibli_v1': 'tuzi_edit',
    'snoopy_v1': 'tuzi_edit',
    'celebrity_selfie_v1': 'tuzi_edit',
    'generic_v1': 'tuzi_chat',
}

# 厂商特性配置
PROVIDER_FEATURES = {
    'aihubmix': {
        'name': 'AIHubMix',
        'description': 'AIHubMix API厂商，使用chat.completions接口',
        'api_base': 'https://aihubmix.com/v1',
        'model': 'gpt-4o-image-vip',
        'interface': 'chat.completions',
        'supports_streaming': True,
        'supports_multi_image': True,
        'supports_n_param': True,
        'max_images_per_request': 4,
        'max_source_images': 10,
        'requires_source_images': False,
        'timeout_default': 6000,
        'env_prefix': 'AIHUBMIX'
    },
    'tuzi_chat': {
        'name': '兔子API-Chat',
        'description': '兔子API厂商，使用chat.completions接口',
        'api_base': 'https://api.tu-zi.com/v1',
        'model': 'gpt-4o-image-vip',
        'interface': 'chat.completions',
        'supports_streaming': True,
        'supports_multi_image': True,
        'supports_n_param': True,
        'max_images_per_request': 4,
        'max_source_images': 10,
        'requires_source_images': False,
        'timeout_default': 6000,
        'env_prefix': 'TUZI'
    },
    'tuzi_edit': {
        'name': '兔子API-Edit',
        'description': '兔子API厂商，使用images.edit接口',
        'api_base': 'https://api.tu-zi.com/v1',
        'model': 'gpt-image-1',
        'interface': 'images.edit',
        'supports_streaming': False,
        'supports_multi_image': True,
        'supports_n_param': False,
        'max_images_per_request': 4,
        'max_source_images': 10,
        'requires_source_images': True,
        'timeout_default': 6000,
        'env_prefix': 'TUZI'
    }
}

# 风格特殊配置（简化版 - 移除n参数相关配置）
STYLE_SPECIAL_CONFIG = {
    # AIHubMix厂商的风格（gpt-4o-image-vip + chat.completions，默认生成2张）
    'cat_travel_v1': {
        'prefill_enabled': True,
        'requires_ratio': True
    },
    'pet_id_photo_v1': {
        'requires_source_images': True,
        'max_source_images': 4,
        'requires_ratio': True
    },
    'japanese_cartoon_v1': {
        'requires_source_images': True,
        'max_source_images': 1,
        'requires_ratio': True
    },

    # 兔子Chat厂商的风格（gpt-4o-image-vip + chat.completions，默认生成2张）
    'cat_travel_v2': {
        'prefill_enabled': True,
        'requires_source_images': True,
        'requires_ratio': True
    },
    'pet_id_photo_v2': {
        'requires_source_images': True,
        'max_source_images': 4,
        'requires_ratio': True
    },
    'generic_v1': {
        'requires_source_images': False,
        'max_source_images': 4,
        'requires_ratio': True
    },

    # 兔子Edit厂商的风格（gpt-image-1 + images.edit，固定生成1张）
    'ghibli_v1': {
        'requires_source_images': True,
        'max_source_images': 1,
        'requires_ratio': True
    },
    'snoopy_v1': {
        'requires_source_images': True,
        'max_source_images': 1,
        'requires_ratio': True
    },
    'celebrity_selfie_v1': {
        'prefill_enabled': True,
        'requires_source_images': True,
        'max_source_images': 4,
        'requires_ratio': True
    }
}

def get_provider_for_style(model_identifier: str) -> str:
    """获取风格对应的厂商名称"""
    return STYLE_PROVIDER_MAPPING.get(model_identifier, 'aihubmix')

def get_provider_config(provider_name: str) -> dict:
    """获取厂商配置"""
    return PROVIDER_FEATURES.get(provider_name, {})

def get_style_config(model_identifier: str) -> dict:
    """获取风格特殊配置"""
    return STYLE_SPECIAL_CONFIG.get(model_identifier, {})

def is_style_supported(model_identifier: str) -> bool:
    """检查风格是否被支持"""
    return model_identifier in STYLE_PROVIDER_MAPPING

def get_all_supported_styles() -> list:
    """获取所有支持的风格列表"""
    return list(STYLE_PROVIDER_MAPPING.keys())

def get_styles_by_provider(provider_name: str) -> list:
    """获取指定厂商的所有风格"""
    return [style for style, provider in STYLE_PROVIDER_MAPPING.items() if provider == provider_name]
