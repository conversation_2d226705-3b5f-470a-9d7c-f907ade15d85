import asyncio
import base64
import logging
import os
import io
from typing import Dict, List, Optional, Any

import httpx
from openai import OpenAI

from config import Config
from .base_provider import BaseProvider

logger = logging.getLogger(__name__)

class TuziProvider(BaseProvider):
    """兔子API厂商实现"""

    def __init__(self):
        # 允许通过环境变量 TUZI_API_KEY 覆盖，默认回退到 Config.AIHUBMIX_API_KEY 以兼容旧配置
        api_key = os.getenv("TUZI_API_KEY", Config.AIHUBMIX_API_KEY)
        if not api_key:
            logger.error("TUZI_API_KEY 未配置，无法使用兔子API厂商")
            raise RuntimeError("TUZI_API_KEY 未配置，无法使用兔子API厂商")

        # API Base 与超时时间
        api_base = os.getenv("TUZI_API_BASE", "https://api.tu-zi.com/v1")
        request_timeout = int(os.getenv("TUZI_TIMEOUT", "6000"))

        self.client = OpenAI(
            api_key=api_key,
            base_url=api_base,
            http_client=httpx.Client(
                timeout=httpx.Timeout(request_timeout, connect=60),
                verify=False  # 兔子证书偶发异常，关闭更稳
            )
        )

        # 关闭 SSL warning
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    async def generate_image(self, prompt: str, source_images: List[str], ratio: str = "1:1") -> List[str]:
        """调用兔子 gpt-image-1 接口生成图片，返回图片 URL 列表"""
        try:
            if not source_images:
                raise RuntimeError("兔子API需要至少一张参考图片")

            # 构建包含ratio的提示词
            # 检查是否已包含ratio参数，避免重复添加
            has_ratio = 'ratio' in prompt.lower() or '比例' in prompt
            if not has_ratio:
                enhanced_prompt = f"{prompt}, ratio {ratio}"
            else:
                enhanced_prompt = prompt

            # 下载参考图
            files: List[io.BytesIO] = []
            for idx, url in enumerate(source_images):
                img_bytes = await self._fetch_image_bytes(url)
                if img_bytes:
                    bio = io.BytesIO(img_bytes)
                    bio.name = f"ref_{idx}.png"
                    files.append(bio)
                else:
                    logger.warning(f"跳过无法下载的图片: {url}")

            if not files:
                raise RuntimeError("请上传参考图片")

            logger.info(f"调用兔子 gpt-image-1 生成图片，参考图 {len(files)} 张，prompt 长度 {len(enhanced_prompt)} 字符，尺寸比例: {ratio}")

            def _call_edit() -> Any:
                try:
                    # 兔子gpt-image-1模型固定生成1张图片，不传递n参数
                    return self.client.images.edit(
                        model="gpt-image-1",
                        image=files,
                        prompt=enhanced_prompt or "生成图片",
                    )
                except Exception as e:
                    logger.error(f"Tuzi API 调用失败: {e}")
                    raise

            result = await asyncio.to_thread(_call_edit)

            if not result or not result.data:
                raise RuntimeError("图片生成失败，请稍后再试")

            output_urls: List[str] = []
            for item in result.data:
                if getattr(item, "url", None):
                    output_urls.append(item.url)
                elif getattr(item, "b64_json", None):
                    try:
                        # 将base64数据转换为临时URL，让download_multiple_images统一处理
                        # 这样可以保持用户分组存储的一致性
                        img_bytes = base64.b64decode(item.b64_json)

                        # 创建临时文件用于传递给下载函数
                        import tempfile
                        with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as temp_file:
                            temp_file.write(img_bytes)
                            temp_file_path = temp_file.name

                        # 返回临时文件路径，让调用方通过download_multiple_images处理
                        output_urls.append(f"file://{temp_file_path}")
                    except Exception as e:
                        logger.error(f"处理 base64 图片失败: {e}")

            if not output_urls:
                raise RuntimeError("API 未返回任何可用的图片链接")

            return output_urls

        except Exception as e:
            logger.error(f"TuziProvider API调用失败: {e}")
            
            # 转换错误信息为用户友好的提示
            error_msg = str(e)
            if "负载" in error_msg or "繁忙" in error_msg or "波动" in error_msg:
                raise RuntimeError("AI服务暂时繁忙，请稍后再试")
            elif "timeout" in error_msg.lower() or "超时" in error_msg:
                raise RuntimeError("生成请求超时，请稍后再试")
            elif "无效" in error_msg or "敏感" in error_msg:
                raise RuntimeError("提示词包含敏感内容，请修改后重试")
            else:
                raise RuntimeError("图片生成失败，请稍后再试")

    async def _fetch_image_bytes(self, url: str) -> Optional[bytes]:
        """下载图片并返回字节数据，支持本地/static 路径或远程HTTP(S)"""
        max_retries = int(os.getenv("TUZI_IMG_RETRIES", "3"))
        retry = 0

        while retry < max_retries:
            try:
                if not url.startswith("http"):
                    # 处理相对路径 -> 转为完整URL
                    if not url.startswith("/"):
                        url = f"/{url}"
                    full_url = f"{Config.BASE_URL}{url}"
                    url_to_get = full_url
                    logger.info(f"下载本地图片: {full_url}")
                else:
                    url_to_get = url

                async with httpx.AsyncClient(timeout=60.0, verify=False) as client:
                    response = await client.get(url_to_get)
                    response.raise_for_status()
                    return response.content

            except Exception as e:
                retry += 1
                logger.warning(f"下载图片失败 ({retry}/{max_retries}): {url}, 错误: {e}")
                if retry < max_retries:
                    await asyncio.sleep(1)

        logger.error(f"最终仍无法下载图片: {url}")
        return None

    def validate_params(self, params: Dict) -> bool:
        """验证输入参数"""
        if params is None:
            raise ValueError("参数不能为空")

        # 至少要有一张源图
        if not params.get("source_image_urls") and not params.get("source_image_url"):
            raise ValueError("兔子API需要上传参考图片")

        if "prompt" in params and len(params["prompt"]) > 3000:
            raise ValueError("提示词长度不能超过3000字符")

        if "n" in params:
            n_val = params["n"]
            if not isinstance(n_val, int) or n_val < 1 or n_val > 4:
                raise ValueError("n参数必须是1-4之间的整数")

        if "source_image_urls" in params:
            lst = params["source_image_urls"]
            if not isinstance(lst, list):
                raise ValueError("source_image_urls必须是列表类型")
            if len(lst) > 10:
                raise ValueError("上传图片数量不能超过10张")

        return True

    @property
    def provider_name(self) -> str:
        """厂商名称标识"""
        return "tuzi"
    
    @property
    def supported_features(self) -> Dict[str, bool]:
        """支持的功能特性"""
        return {
            'multi_image': True,
            'streaming': False,
            'chat_completions': False,
            'images_edit': True,
            'max_images_per_request': 4,
            'max_source_images': 10,
            'requires_source_images': True
        }
