import asyncio
import logging
import base64
import re
import httpx
import os
from typing import Dict, Optional, List, Any
from openai import OpenAI
from config import Config
from .base_provider import BaseProvider

logger = logging.getLogger(__name__)

class AihubmixProvider(BaseProvider):
    """AIHubMix API厂商实现"""

    def __init__(self):
        if not Config.AIHUBMIX_API_KEY:
            logger.error("AIHUBMIX_API_KEY 未配置，无法使用AIHubMix厂商")
            raise RuntimeError("AIHUBMIX_API_KEY 未配置，无法使用AIHubMix厂商")
        
        # 请求超时时间，可通过环境变量 AIHUBMIX_TIMEOUT 调整，默认 6000 秒
        request_timeout = int(os.getenv("AIHUBMIX_TIMEOUT", "6000"))
        ai_base = os.getenv("AIHUBMIX_API_BASE", "https://aihubmix.com/v1")

        self.client = OpenAI(
            api_key=Config.AIHUBMIX_API_KEY,
            base_url=ai_base,
            http_client=httpx.Client(
                timeout=httpx.Timeout(request_timeout, connect=60),
                verify=False  # AIHubMix 证书偶尔会报 SNI 错误，关闭校验更稳
            )
        )
        
        # 禁用SSL警告
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    async def generate_image(self, prompt: str, source_images: List[str], ratio: str = "1:1") -> List[str]:
        """调用 AIHubMix GPT-4o-image 接口生成图片"""
        try:
            # 构建基本提示词，只添加ratio参数
            # 不添加n参数，让模型使用默认值（通常是2张）
            has_ratio = '"ratio":' in prompt or 'ratio' in prompt.lower()

            base_prompt = prompt
            if not has_ratio:
                base_prompt = f"{base_prompt}\n\"ratio\": \"{ratio}\""

            # 只在出错时才输出详细日志，成功时保持简洁

            # 生成图片并获取URL列表，不传递n参数
            return await self._generate_with_chat(base_prompt, source_images)
            
        except Exception as e:
            logger.error(f"AihubmixProvider API调用失败: {e}")
            
            # 转换错误信息为用户友好的提示
            error_msg = str(e)
            if "负载" in error_msg or "繁忙" in error_msg or "波动" in error_msg:
                raise RuntimeError("AI服务暂时繁忙，请稍后再试")
            elif "timeout" in error_msg.lower() or "超时" in error_msg:
                raise RuntimeError("生成请求超时，请稍后再试")
            elif "无效" in error_msg or "敏感" in error_msg:
                raise RuntimeError("提示词包含敏感内容，请修改后重试")
            elif "验证生成图片URL失败" in error_msg or "无法访问" in error_msg:
                raise RuntimeError("图片生成成功但无法下载，请稍后再试")
            else:
                raise RuntimeError("图片生成失败，请稍后再试")

    async def _generate_with_chat(self, prompt: str, image_urls: List[str] = None) -> List[str]:
        """统一的图片生成方法，支持纯文本和图片输入，返回一个或多个图片URL"""
        try:

            # 构建消息内容
            chat_content: List[Dict[str, Any]] = [
                {
                    "type": "text",
                    "text": prompt
                }
            ]
            
            # 如果有图片URL，添加图片内容
            if image_urls and len(image_urls) > 0:
                for image_url in image_urls:
                    try:
                        # 获取图片并转换为base64
                        base64_image = await self._get_image_as_base64(image_url)
                        if not base64_image:
                            logger.warning(f"无法获取或编码图片: {image_url}，跳过该图片")
                            continue
                        
                        # 图片MIME类型
                        img_mime = "image/jpeg"  # 默认JPEG
                        if image_url.lower().endswith(".png"):
                            img_mime = "image/png"
                        
                        # 添加图片到内容中
                        chat_content.append({
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:{img_mime};base64,{base64_image}"
                            }
                        })
                        logger.info(f"成功添加图片: {image_url}")
                    except Exception as e:
                        logger.error(f"处理图片 {image_url} 时出错: {str(e)}")
                        # 继续处理下一张图片，不中断流程
            
            # 异步包装同步API调用（使用流式，提高超时容忍度）
            def _call_chat_api():
                try:
                    
                    stream_iter = self.client.chat.completions.create(
                        model="gpt-4o-image-vip",
                        messages=[
                            {
                                "role": "user",
                                "content": chat_content
                            }
                        ],
                        stream=True  # 使用流式，避免一次性长时间阻塞
                    )

                    buffer: str = ""
                    image_urls: List[str] = []  # 存储所有找到的图片URL
                    
                    # 辅助函数：将 /cdn/download/xxx.png 归一化为 /cdn/xxx.png
                    def _normalize_url(u: str) -> str:
                        # 只对 filesystem.site 地址做归一化
                        if "filesystem.site" in u and "/cdn/download/" in u:
                            return u.replace("/cdn/download/", "/cdn/")
                        return u

                    # 记录已收集的归一化URL，避免同图重复
                    canonical_set: set[str] = set()

                    for chunk in stream_iter:
                        if (not chunk.choices or
                                not chunk.choices[0].delta or
                                chunk.choices[0].delta.content is None):
                            continue
                        token = chunk.choices[0].delta.content
                        buffer += token

                        # 检查是否包含负载过高的提示
                        if "失败原因: 当前 OpenAI 服务出现网络波动或者负载过高" in buffer or "❌ 生成失败" in buffer:
                            logger.warning("检测到AI服务负载过高的提示")
                            raise RuntimeError("AI服务暂时繁忙，请稍后再试")

                        # 只提取 filesystem.site 链接，忽略其他所有链接
                        fs_matches = re.finditer(r'(https?://filesystem\.site/cdn(?:/download)?/[^\s\)\"\']+\.(?:png|jpg|jpeg|gif|webp))', buffer)
                        for match in fs_matches:
                            url = match.group(1)
                            canon = _normalize_url(url)
                            if canon not in canonical_set:
                                # 优先存储不带 /download 的版本
                                preferred = canon if canon == url else canon
                                image_urls.append(preferred)
                                canonical_set.add(canon)

                        # 不再匹配其他域名的链接，只使用filesystem.site的链接

                    # 如果流结束后未提取到任何URL
                    if not image_urls:
                        logger.error(f"流式响应结束但未提取到图片URL，响应内容片段: {buffer[:300]}")

                        # 检查响应中是否包含特定错误信息
                        if "失败原因" in buffer or "❌ 生成失败" in buffer:
                            raise RuntimeError("AI服务暂时繁忙，请稍后再试")
                        else:
                            raise RuntimeError("图片生成失败，请稍后再试")

                    # 返回找到的所有filesystem.site URL
                    logger.info(f"AIHubMix生成完成，收集到 {len(image_urls)} 张图片")
                    return image_urls
                except Exception as e:
                    logger.error(f"API调用出错: {str(e)}")
                    
                    # 转换技术错误为用户友好的错误信息
                    error_msg = str(e)
                    if "无法从API" in error_msg or "提取图片URL" in error_msg:
                        raise RuntimeError("图片生成失败，请稍后再试")
                    elif "timeout" in error_msg.lower() or "超时" in error_msg:
                        raise RuntimeError("生成请求超时，请稍后再试")
                    elif "负载" in error_msg or "繁忙" in error_msg or "波动" in error_msg:
                        raise RuntimeError("AI服务暂时繁忙，请稍后再试")
                    else:
                        raise RuntimeError("图片生成失败，请稍后再试")

            # 执行API调用并等待结果
            image_urls = await asyncio.to_thread(_call_chat_api)
            logger.info(f"图片生成成功，返回 {len(image_urls)} 个URL")
            
            # 验证生成的图片URL是否可访问（延长超时并增加重试）
            valid_urls: List[str] = []
            for url in image_urls:
                logger.info(f"验证生成的图片是否可访问: {url}")

                # 直接信任 filesystem.site 域名
                if "filesystem.site" in url:
                    valid_urls.append(url)
                    continue

                max_attempts = int(os.getenv("AIHUBMIX_URL_RETRIES", "5"))
                attempt = 0
                while attempt < max_attempts:
                    try:
                        async with httpx.AsyncClient(verify=False, timeout=60.0) as client:
                            response = await client.head(url)
                            if response.status_code == 200:
                                valid_urls.append(url)
                                logger.info(f"URL 验证成功: {url}")
                                break
                            else:
                                logger.warning(f"URL 返回状态 {response.status_code}，第 {attempt+1}/{max_attempts} 次: {url}")
                    except Exception as e:
                        logger.warning(f"URL 验证异常 (第 {attempt+1}/{max_attempts} 次): {e}")
                    attempt += 1
                    await asyncio.sleep(2)

                if attempt >= max_attempts and url not in valid_urls:
                    logger.error(f"URL 多次重试后仍无法访问: {url}")
            
            # 如果没有有效URL，抛出异常
            if not valid_urls:
                raise RuntimeError("所有生成的图片URL均无法访问，请重试")
                
            return valid_urls
        except Exception as e:
            logger.error(f"图片生成调用失败: {str(e)}")
            
            # 转换错误信息为用户友好的提示
            error_msg = str(e)
            if "负载" in error_msg or "繁忙" in error_msg or "波动" in error_msg:
                raise RuntimeError("AI服务暂时繁忙，请稍后再试")
            elif "timeout" in error_msg.lower() or "超时" in error_msg:
                raise RuntimeError("生成请求超时，请稍后再试")
            elif "无效" in error_msg or "敏感" in error_msg:
                raise RuntimeError("提示词包含敏感内容，请修改后重试")
            elif "验证生成图片URL失败" in error_msg or "无法访问" in error_msg:
                raise RuntimeError("图片生成成功但无法下载，请稍后再试")
            else:
                raise RuntimeError("图片生成失败，请稍后再试")

    async def _get_image_as_base64(self, image_url: str) -> Optional[str]:
        """获取图片并转换为 base64 编码（本地或远程均可）"""
        max_retries = int(os.getenv("AIHUBMIX_IMG_RETRIES", "5"))
        retry_count = 0

        while retry_count < max_retries:
            try:
                # 处理本地图片（相对路径）
                if not image_url.startswith("http"):
                    # 确保路径以 / 开头
                    if not image_url.startswith("/"):
                        image_url = f"/{image_url}"

                    full_url = f"{Config.BASE_URL}{image_url}"
                    logger.info(f"下载本地图片: {full_url}")

                    async with httpx.AsyncClient(verify=False, timeout=60.0) as client:
                        response = await client.get(full_url)
                        response.raise_for_status()
                        image_bytes = response.content
                else:
                    # 下载远程图片
                    async with httpx.AsyncClient(verify=False, timeout=60.0) as client:
                        response = await client.get(image_url)
                        response.raise_for_status()
                        image_bytes = response.content

                # 成功获取图片字节后立即编码并返回
                return base64.b64encode(image_bytes).decode("utf-8")

            except Exception as e:
                retry_count += 1
                logger.error(f"获取或编码图片失败 (尝试 {retry_count}/{max_retries}): {e}")
                if retry_count >= max_retries:
                    logger.error(f"达到最大重试次数，获取图片失败: {image_url}")
                    return None
                await asyncio.sleep(1)  # 等待 1 秒后重试

    def validate_params(self, params: Dict) -> bool:
        """验证输入参数"""
        if params is None:
            raise ValueError("参数不能为空")

        # prompt 可为空（FULL_PROMPT 类型）
        if "prompt" in params and len(params["prompt"]) > 3000:
            raise ValueError("提示词长度不能超过3000字符")

        # 验证n参数
        if "n" in params:
            n = params["n"]
            if not isinstance(n, int) or n < 1 or n > 4:
                raise ValueError("n参数必须是1-4之间的整数")

        # 验证source_image_urls参数
        if "source_image_urls" in params:
            source_image_urls = params["source_image_urls"]
            if not isinstance(source_image_urls, list):
                raise ValueError("source_image_urls必须是列表类型")

            # 限制最大图片数量
            if len(source_image_urls) > 10:  # 设置一个合理的上限
                raise ValueError("上传图片数量不能超过10张")

        return True

    @property
    def provider_name(self) -> str:
        """厂商名称标识"""
        return "aihubmix"

    @property
    def supported_features(self) -> Dict[str, bool]:
        """支持的功能特性"""
        return {
            'multi_image': True,
            'streaming': True,
            'chat_completions': True,
            'images_edit': False,
            'max_images_per_request': 4,
            'max_source_images': 10
        }
