from abc import ABC, abstractmethod
from typing import Dict, List, Optional

class BaseProvider(ABC):
    """API厂商抽象基类"""
    
    @abstractmethod
    async def generate_image(self, prompt: str, source_images: List[str], ratio: str = "1:1") -> List[str]:
        """生成图片的核心方法

        Args:
            prompt: 提示词
            source_images: 源图片URL列表
            ratio: 图片尺寸比例，如 "1:1", "16:9", "9:16" 等

        Returns:
            List[str]: 生成的图片URL列表

        Note:
            支持n参数的模型（gpt-4o-image-vip）默认生成2张图片
            不支持n参数的模型（gpt-image-1）固定生成1张图片
        """
        raise NotImplementedError
    
    @abstractmethod
    def validate_params(self, params: Dict) -> bool:
        """验证输入参数
        
        Args:
            params: 生成参数字典
            
        Returns:
            bool: 参数是否有效
        """
        raise NotImplementedError
    
    @property
    @abstractmethod
    def provider_name(self) -> str:
        """厂商名称标识"""
        raise NotImplementedError
    
    @property
    @abstractmethod
    def supported_features(self) -> Dict[str, bool]:
        """支持的功能特性
        
        Returns:
            Dict[str, bool]: 功能特性字典，如 {'multi_image': True, 'streaming': False}
        """
        raise NotImplementedError
    
    def get_default_params(self) -> Dict:
        """获取默认参数"""
        return {
            'n': 1,
            'max_source_images': 10,
            'timeout': 300
        }
