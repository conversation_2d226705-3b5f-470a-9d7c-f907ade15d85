import logging
from database.connection import db_manager
from database.schema_validator import schema_validator
from database.models import Base, Style, TemplateType
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

class DatabaseInitializer:
    """数据库初始化器"""
    
    def __init__(self):
        self.db_manager = db_manager
        self.validator = schema_validator
    
    def initialize_database(self):
        """完整的数据库初始化流程"""
        try:
            logger.info("⚙️ 正在初始化数据库...")
            
            # 1. 初始化数据库连接
            self.db_manager.init_database()
            
            # 2. 初始化校验器
            self.validator.init_validator(self.db_manager.engine)
            
            # 3. 创建缺失的表
            self.validator.create_missing_tables()
            
            # 4. 重新校验表结构（表创建后）
            issues = self.validator.validate_all_tables()
            
            if issues:
                logger.warning("⚠️ 发现数据库结构问题:")
                self.validator._log_issues(issues)
                self.validator.auto_fix_schema_issues(issues)
            else:
                logger.info("✓ 数据库结构校验通过")
            
            # 5. 初始化基础数据
            self._initialize_base_data()
            
            logger.info("✓ 数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _initialize_base_data(self):
        """初始化基础数据"""
        try:
            db = next(self.db_manager.get_db())
            
            # 检查是否已有风格数据
            existing_styles = db.query(Style).count()
            if existing_styles == 0:
                logger.info("✓ 初始化基础风格数据 ({existing_styles}个风格)")
                self._create_default_styles(db)
                db.commit()
            else:
                logger.info(f"✓ 已存在 {existing_styles} 个风格模板，检查配置完整性")

                # 补充宠物证件照风格的 settings
                pet_style = db.query(Style).filter(Style.model_identifier == 'pet_id_photo_v1').first()
                if pet_style:
                    settings = pet_style.settings or {}
                    if 'max_source_images' not in settings:
                        settings['max_source_images'] = 1
                        pet_style.settings = settings
                        db.commit()
                        logger.info("✓ 已自动补齐 pet_id_photo_v1 的 max_source_images = 1")
                
                # 检查默认风格是否都已存在，如果有缺失则添加
                self._check_and_add_missing_styles(db)
                
        except Exception as e:
            logger.error(f"初始化基础数据失败: {e}")
            db.rollback()
            raise
        finally:
            db.close()
            
    def _check_and_add_missing_styles(self, db: Session):
        """检查并添加缺失的默认风格"""
        # 获取默认风格配置
        default_styles = self._get_default_styles()
        
        # 获取已存在风格的名称列表
        existing_style_names = [style.name for style in db.query(Style.name).all()]
        
        # 检查每个默认风格是否存在
        added_count = 0
        for style_data in default_styles:
            if style_data['name'] not in existing_style_names:
                # 添加缺失的风格
                style = Style(**style_data)
                db.add(style)
                added_count += 1
                logger.info(f"✓ 添加新风格: {style_data['name']}")
        
        if added_count > 0:
            db.commit()
            logger.info(f"✓ 共添加了 {added_count} 个新风格")
        else:
            logger.info("✓ 所有默认风格已存在，无需添加")
    
    def _get_default_styles(self):
        """获取默认风格数据列表"""
        return [
            {
                'name': '吉卜力风格',
                'description': '闯入动漫世界',
                'cover_image_url': '/static/styles/ghibli.png',
                'template_type': TemplateType.FULL_PROMPT,
                'prompt_template': '将上传的照片转换为吉卜力风格，保持全部内容和细节不变。',
                'template_variables': None,
                'model_identifier': 'ghibli_v1',
                'cost': 30,
                'example_images': None,
                'settings': {},
                'is_popular': 0
            },
            {
                'name': '猫咪去旅行',
                'description': '带主子环游世界',
                'cover_image_url': '/static/styles/cat_travel.jpg',
                'template_type': TemplateType.VARIABLE_PROMPT,
                'prompt_template': '请你生成张极其平凡无奇的iPhone 自拍照，没有明确的主体或者构图感，就是随手一拍的快照，照片略带运动模糊，打光不均导致的轻微曝光过度，整体呈现出一种刻意的平庸感，就像是从口袋里拿手机时不小心拍到的一张自拍。{detail}',
                'template_variables': [
                    {"name":"detail","label":"描述"}
                ],
                'model_identifier': 'cat_travel_v1',
                'cost': 40,
                'example_images': [
                    '/static/styles/examples/cat_travel_1.jpg',
                    '/static/styles/examples/cat_travel_2.jpg'
                ],
                'settings': {},
                'is_popular': 5
            },
            {
                'name': '宠物证件照',
                'description': '萌宠专属证件照',
                'cover_image_url': '/static/styles/pet_id_photo.jpg',
                'template_type': TemplateType.VARIABLE_PROMPT,
                'prompt_template': '参考这张图片，帮我生成一张证件照，{background_style}，一寸大小',
                'template_variables': [
                    {"name": "background_style", "label": "背景样式", "type": "select", 
                     "options": [
                         {"value": "白底", "label": "白底"},
                         {"value": "蓝底", "label": "蓝底"},
                         {"value": "custom", "label": "自定义", "custom_field": True, 
                          "custom_placeholder": "白底，戴上红色领结"}
                     ]
                    }
                ],
                'model_identifier': 'pet_id_photo_v1',
                'cost': 30,
                'example_images': [
                    '/static/styles/examples/pet_id_photo.jpg'
                ],
                'settings': {
                    'max_source_images': 1
                },
                'is_popular': 10
            },
            {
                'name': '史努比风格',
                'description': '卡通狗狗的世界',
                'cover_image_url': '/static/styles/snoopy.jpg',
                'template_type': TemplateType.FULL_PROMPT,
                'prompt_template': '把图片变成snoopy风格的卡通图片，同时加上一只snoopy和Woodstock',
                'template_variables': None,
                'model_identifier': 'snoopy_v1',
                'cost': 30,
                'example_images': [
                    '/static/styles/examples/snoopy.jpg'
                ],
                'settings': {
                    'max_source_images': 1
                },
                'is_popular': 0
            },
            {
                'name': '日本小人插画',
                'description': '可爱日式插画风格',
                'cover_image_url': '/static/styles/japanese_cartoon.jpg',
                'template_type': TemplateType.FULL_PROMPT,
                'prompt_template': '帮我把照片生成 いらすとや 风格的图片',
                'template_variables': None,
                'model_identifier': 'japanese_cartoon_v1',
                'cost': 30,
                'example_images': [
                    '/static/styles/examples/japanese_cartoon_1.jpg',
                    '/static/styles/examples/japanese_cartoon_2.jpg'
                ],
                'settings': {
                    'max_source_images': 1
                },
                'is_popular': 0
            },
            {
                'name': '名人合照',
                'description': '与名人同框自拍',
                'cover_image_url': '/static/styles/celebrity_selfie.jpg',
                'template_type': TemplateType.VARIABLE_PROMPT,
                'prompt_template': '请你生成张极其平凡无奇的iPhone 自拍照，没有明确的主体或者构图感，就是随手一拍的快照，照片略带运动模糊，打光不均导致的轻微曝光过度，整体呈现出一种刻意的平庸感，就像是从口袋里拿手机时不小心拍到的一张自拍。{detail}',
                'template_variables': [
                    {"name": "detail", "label": "描述"}
                ],
                'model_identifier': 'celebrity_selfie_v1',
                'cost': 40,
                'example_images': [
                    '/static/styles/examples/celebrity_selfie_1.jpg'
                ],
                # 不对上传图片数量做限制，沿用前端默认（4张）
                'settings': {},
                'is_popular': 8
            },
            {
                'name': '自由创作',
                'description': '不限风格，自由提示词',
                'cover_image_url': '/static/styles/generic.png',
                'template_type': TemplateType.VARIABLE_PROMPT,
                'prompt_template': '',
                'template_variables': [
                    {'name': 'detail', 'label': '提示词', 'type': 'text'}
                ],
                'model_identifier': 'generic_v1',
                'cost': 50,
                'example_images': None,
                'settings': {},
                'is_popular': 0
            }
        ]
        
    def _create_default_styles(self, db: Session):
        """创建默认风格数据"""
        default_styles = self._get_default_styles()
        
        for style_data in default_styles:
            style = Style(**style_data)
            db.add(style)

# 全局初始化器实例
db_initializer = DatabaseInitializer()